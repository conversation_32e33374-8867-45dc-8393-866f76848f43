import React, { useState, useEffect } from 'react';

import {
    View,
    Text,
    StyleSheet,
    Image,
    TouchableOpacity,
    ImageBackground,
    Animated,
    StatusBar,
    Platform,
    ScrollView,
    RefreshControl,
    Alert
} from 'react-native';

import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';

import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useSQLiteContext } from 'expo-sqlite';
import { getMoodImage } from '../appGlobalData';

import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon, Delete02Icon } from '@hugeicons/core-free-icons';

export default function indexSomedayNoteListScreen() {
    const insets = useSafeAreaInsets(); // 获取安全区域的边距
    const db = useSQLiteContext(); // 获取SQLite数据库上下文

    // 获取传递过来的参数
    const { year, month, day } = useLocalSearchParams();

    // 添加状态来存储查询到的笔记数据
    const [notes, setNotes] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    const handleTopLeftBack = () => {
        router.back();
    };

    // 格式化日期显示
    const formatDate = () => {
        if (year && month && day) {
            return `${year}年${month}月${day}日`;
        }
        return '选定日期';
    };

    // 查询当日的日记数据
    const loadNotesForDate = async () => {
        if (!year || !month || !day) {
            console.log('缺少日期参数');
            return;
        }

        setIsLoading(true);

        try {
            // 构造查询的日期字符串 (YYYY-MM-DD format)
            const queryDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

            console.log('查询日期:', queryDate);

            // 查询指定日期的所有笔记
            const query = `
                SELECT * FROM notes 
                WHERE date(created_at) = date(?)
                ORDER BY created_at DESC
            `;

            const result = await db.getAllAsync(query, [queryDate]);

            console.log(`${queryDate} 的笔记数量:`, result.length);
            console.log('查询结果:', result);

            setNotes(result as any[]);

        } catch (error) {
            console.error('查询笔记时出错:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // 页面初始化时查询数据
    useEffect(() => {
        loadNotesForDate();
    }, [year, month, day]);

    // 使用 useFocusEffect 来在页面重新获得焦点时刷新数据
    useFocusEffect(
        React.useCallback(() => {
            console.log('页面获得焦点，刷新当日笔记数据');
            loadNotesForDate();
        }, [year, month, day])
    );

    // 移除HTML标签，获取纯文本预览
    const getTextPreview = (htmlContent: string) => {
        const textContent = htmlContent.replace(/<[^>]*>/g, '');
        return textContent.length > 80 ? textContent.substring(0, 80) + '...' : textContent;
    };

    // 格式化时间显示
    const formatTime = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };


    // 删除笔记函数
    const deleteNote = async (noteId: number, noteTitle: string) => {
        Alert.alert(
            '确认删除',
            `确定要删除笔记"${noteTitle}"吗？此操作无法撤销。`,
            [
                {
                    text: '取消',
                    style: 'cancel',
                },
                {
                    text: '删除',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await db.runAsync('DELETE FROM notes WHERE id = ?', [noteId]);
                            console.log('成功删除笔记:', noteId);

                            // 删除后重新加载数据
                            loadNotesForDate();

                            Alert.alert('删除成功', '笔记已删除');
                        } catch (error) {
                            console.error('删除笔记时出错:', error);
                            Alert.alert('删除失败', '删除笔记时发生错误，请重试');
                        }
                    },
                },
            ]
        );
    };

    // 渲染笔记项
    const renderNoteItem = (note: any) => (
        <TouchableOpacity
            key={note.id}
            style={styles.noteItem}
            onPress={() => {
                // 这里可以添加点击笔记后的操作，比如查看详情
                console.log('点击了笔记:', note.id);
            }}
        >
            <View style={styles.noteContent}>
                <View style={styles.noteHeader}>
                    <Image
                        source={getMoodImage(note.mood_app_in_img_name)}
                        style={styles.moodImage}
                    />
                    <View style={styles.noteInfo}>
                        <Text style={styles.noteTime}>{formatTime(note.created_at)}</Text>
                        <View style={styles.moodAndSyncContainer}>
                            <Text style={styles.noteMood}>{note.mood_name}</Text>
                            {note.mongodb_id ? (
                                <View style={styles.syncedStatusTag}>
                                    <Text style={styles.syncedStatusText}>已同步</Text>
                                </View>
                            ) : (
                                <View style={styles.syncStatusTag}>
                                    <Text style={styles.syncStatusText}>等待同步</Text>
                                </View>
                            )}
                        </View>
                    </View>
                    <TouchableOpacity
                        style={styles.deleteButton}
                        onPress={(e) => {
                            e.stopPropagation(); // 防止触发父组件的点击事件
                            deleteNote(note.id, note.title);
                        }}
                    >
                        <HugeiconsIcon
                            icon={Delete02Icon}
                            size={20}
                            color="#ff4444"
                            strokeWidth={1.5}
                        />
                    </TouchableOpacity>
                </View>
                <Text style={styles.noteTitle}>{note.title}</Text>
                <Text style={styles.notePreview}>{getTextPreview(note.content)}</Text>
            </View>
        </TouchableOpacity>
    );


    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}>{formatDate()} 笔记</Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000" // 设置为透明
                            strokeWidth={1.2}
                        />
                    </View>

                    <View style={styles.content}>
                        <Text style={styles.dateInfo}>
                            {notes.length}
                        </Text>

                        <ScrollView
                            style={styles.notesContainer}
                            refreshControl={
                                <RefreshControl
                                    refreshing={isLoading}
                                    onRefresh={loadNotesForDate}
                                />
                            }
                        >
                            {notes.length === 0 ? (
                                <View style={styles.emptyContainer}>
                                    <Text style={styles.emptyText}>
                                        {isLoading ? '正在加载...' : '这一天还没有笔记哦~'}
                                    </Text>
                                    <Text style={styles.emptySubText}>
                                        {!isLoading && '快去记录今天的心情吧！'}
                                    </Text>
                                </View>
                            ) : (
                                notes.map(renderNoteItem)
                            )}
                        </ScrollView>
                    </View>
                </View>
            </ImageBackground>
        </>

    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,
        width: '100%',
        height: '100%',
    },

    all: {
        flex: 1,
    },

    head_nav: {
        height: 50,
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 16,
    },
    head_nav_title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },

    content: {
        flex: 1,
        paddingHorizontal: 16,
        paddingBottom: 16,
    },

    dateInfo: {
        fontSize: 18,
        color: '#333',
        textAlign: 'center',
        marginBottom: 16,
        fontWeight: '600',
    },

    notesContainer: {
        flex: 1,
    },

    noteItem: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,

    },

    noteContent: {
        flex: 1,
    },

    noteHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },

    moodImage: {
        width: 32,
        height: 32,
        borderRadius: 16,
        marginRight: 12,
    },

    noteInfo: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },

    noteTime: {
        fontSize: 12,
        color: '#666',
        fontWeight: '500',
    },

    moodAndSyncContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 6,
    },

    noteMood: {
        fontSize: 12,
        color: '#333',
        backgroundColor: 'rgba(0, 0, 0, 0.05)',
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 10,
    },

    syncStatusTag: {
        backgroundColor: 'rgba(255, 165, 0, 0.15)',
        paddingHorizontal: 6,
        paddingVertical: 1,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: 'rgba(255, 165, 0, 0.3)',
    },

    syncStatusText: {
        fontSize: 10,
        color: '#ff8c00',
        fontWeight: '500',
    },

    syncedStatusTag: {
        backgroundColor: 'rgba(34, 197, 94, 0.15)',
        paddingHorizontal: 6,
        paddingVertical: 1,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: 'rgba(34, 197, 94, 0.3)',
    },

    syncedStatusText: {
        fontSize: 10,
        color: '#22c55e',
        fontWeight: '500',
    },

    noteTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 6,
        lineHeight: 20,
    },

    notePreview: {
        fontSize: 14,
        color: '#666',
        lineHeight: 18,
    },

    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: 80,
    },

    emptyText: {
        fontSize: 18,
        color: '#666',
        textAlign: 'center',
        marginBottom: 8,
        fontWeight: '500',
    },

    emptySubText: {
        fontSize: 14,
        color: '#999',
        textAlign: 'center',
    },

    // 移除不需要的样式
    placeholderText: {
        fontSize: 16,
        color: '#666',
        textAlign: 'center',
        marginTop: 20,
    },

    debugText: {
        fontSize: 12,
        color: '#999',
        textAlign: 'center',
        marginTop: 10,
    },

    deleteButton: {
        padding: 4,
        borderRadius: 6,
        backgroundColor: 'rgba(255, 68, 68, 0.1)',
        marginLeft: 8,
    },
});
