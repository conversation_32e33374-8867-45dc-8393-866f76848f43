import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    Image,
    TouchableOpacity,
    ImageBackground,
    Animated,
    StatusBar,
    Platform
} from 'react-native';

import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Storage from 'expo-sqlite/kv-store';

import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons';
import { ArrowLeft02Icon } from '@hugeicons/core-free-icons';



export default function DetailsScreen() {
    const insets = useSafeAreaInsets(); // 获取安全区域的边距
    const navigation = useNavigation(); // 获取导航对象
    const [storedValue, setStoredValue] = useState<string>('');

    // 添加 useEffect 在组件挂载时获取数据
    useEffect(() => {
        getData('username');

        fetch_some_data();
    }, []); // 空依赖数组表示只在组件挂载时执行一次

    // 提取函数处理返回操作
    const handleGoBack = () => {
        navigation.goBack();
    };

    const fetch_some_data = async () => {
        try {
            console.log('开始请求数据...');
            // 检查平台
            const platform = Platform.OS;
            console.log(`当前平台: ${platform}`);
            
            const apiUrl = 'https://hongweizhu.com:3000/x_mooood_note';
            // const apiUrl = 'https://baidu.com';

            console.log(`请求URL: ${apiUrl}`);
            
            const res = await fetch(apiUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
              },
              body: JSON.stringify({ test: 'hello', platform: platform }),
            });
            
            console.log('响应状态:', res.status);
            const json = await res.json();
            console.log('响应数据:', JSON.stringify(json));
          } catch (error) {
            // 更详细的错误处理
            if (error instanceof TypeError) {
              console.log('网络请求类型错误:', error);
            } else {
              console.log('请求错误类型:', typeof error);
              console.log('请求错误详情:', error);
            }
        }
    }

    // 数据存储和获取函数
    const storeData = async (key: string, value: string) => {
        try {
            await Storage.setItem(key, value);
            console.log('保存成功');
            setStoredValue(value || '');
        } catch (e) {
            console.error("保存失败:", e);
        }
    };

    const getData = async (key: string) => {
        try {
            const value = await Storage.getItem(key);
            console.log('获取成功');
            setStoredValue(value || '');
            return value || '';
        } catch (e) {
            console.error("获取失败:", e);
            return '';
        }
    };

    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />

                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleGoBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}>详情</Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000" // 设置为透明
                            strokeWidth={1.2}
                        />
                    </View>

                    <View style={styles.content}>
                        <View style={styles.buttonContainer}>
                            <TouchableOpacity
                                style={styles.button}
                                onPress={() => storeData('username', new Date().toLocaleString('zh-CN', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    second: '2-digit'
                                }))}
                            >
                                <Text style={styles.buttonText}>保存数据</Text>
                            </TouchableOpacity>

                            {/* <TouchableOpacity
                        style={styles.button}
                        onPress={() => getData('username')}
                    >
                        <Text style={styles.buttonText}>获取数据</Text>
                    </TouchableOpacity> */}

                        </View>

                        <View style={styles.resultContainer}>
                            <Text style={styles.valueText}>
                                存储的值: {storedValue || '暂无数据'}
                            </Text>
                        </View>
                    </View>
                </View>
            </ImageBackground>
        </>
    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,

        width: '100%',
        height: '100%',
    },
    all: {
        flex: 1,

        justifyContent: 'center',
        alignItems: 'center',
    },
    head_nav: {
        height: 50,
        width: '100%',

        // paddingHorizontal: 5,

        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',

        // backgroundColor: '#f40',
    },
    head_nav_title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },

    content: {
        flex: 1,
        width: '100%',

        // backgroundColor: '#f40',
    },
    
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        padding: 20,
        color: '#333',
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        padding: 20,
    },
    button: {
        backgroundColor: 'black',
        padding: 15,
        borderRadius: 8,
        minWidth: 120,
    },
    buttonText: {
        color: 'white',
        textAlign: 'center',
        fontSize: 16,
        fontWeight: '600',
    },
    resultContainer: {
        margin: 20,
        padding: 20,
        backgroundColor: '#f8f8f8',
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#e0e0e0',
    },
    valueText: {
        fontSize: 18,
        color: '#333',
        textAlign: 'center',
    },
});