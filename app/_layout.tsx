//  before use expo-sqlite
// import { Stack } from "expo-router";

// export default function RootLayout() {
//   return (
//     <Stack screenOptions={{ headerShown: false }}>
//       <Stack.Screen name="index" />
//       <Stack.Screen name="details" />
//       <Stack.Screen name="editnote" />
//       <Stack.Screen name="testsql" />
//     </Stack>
//   );
// }



import React, { Suspense } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Stack } from "expo-router";
import { SQLiteDatabase, SQLiteProvider, useSQLiteContext } from 'expo-sqlite';
import { useFonts } from 'expo-font';
import Toast from 'react-native-toast-message';


export default function RootLayout() {
  // 预加载字体
  const [fontsLoaded] = useFonts({
    'Brushzing-Regular': require('../assets/fonts/Brushzing-Regular.otf'),
    'Biscuitos': require('../assets/fonts/Biscuitos.otf'),
    'BlueBubbles-gwE7P': require('../assets/fonts/BlueBubbles-gwE7P.otf'),
    'CuteAurora-PK3lZ': require('../assets/fonts/CuteAurora-PK3lZ.ttf'),
    // 'DatangStory-drxBK': require('../assets/fonts/DatangStory-drxBK.otf'),
    // 'Cecil1-nZDg': require('../assets/fonts/Cecil1-nZDg.ttf'),
    // '2Peas4ThOfJuly-ARj6': require('../assets/fonts/2Peas4ThOfJuly-ARj6.ttf'),
    // 'desard': require('../assets/fonts/desard.otf'),
    // 'Diet Again': require('../assets/fonts/Diet Again.otf'),
    // 'FarahJawhar-Regular': require('../assets/fonts/FarahJawhar-Regular.otf'),
    // 'FearFluid-Zpp13': require('../assets/fonts/FearFluid-Zpp13.ttf'),
    // 'hipch___': require('../assets/fonts/hipch___.ttf'),
    // 'Idealy-V4J6B': require('../assets/fonts/Idealy-V4J6B.otf'),
    // 'InFormal_Style_Regular': require('../assets/fonts/InFormal_Style_Regular.otf'),
    // 'Kenangan-Regular': require('../assets/fonts/Kenangan-Regular.otf'),
    // 'MessyHandwritten-Regular': require('../assets/fonts/MessyHandwritten-Regular.ttf'),
    // 'MotleyForcesRegular-w1rZ3': require('../assets/fonts/MotleyForcesRegular-w1rZ3.ttf'),
    // 'PajamaPants': require('../assets/fonts/PajamaPants.ttf'),
    // 'PermanentMarker-Regular': require('../assets/fonts/PermanentMarker-Regular.ttf'),
    // 'Sorcerous_Mouse': require('../assets/fonts/Sorcerous_Mouse.otf'),
    // 'SpaceMono-Regular': require('../assets/fonts/SpaceMono-Regular.ttf'),
    // 'Sunny Spells': require('../assets/fonts/Sunny Spells.ttf'),
    'Whitenice': require('../assets/fonts/Whitenice.otf'),
  });

  const createDbIfNeeded = async (db: SQLiteDatabase) => {
    console.log('create db if needed');
    
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        text TEXT
      );
    `);

    // 创建完整的 notes 表（包含所有需要的列）
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS notes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,                        -- 主键自增 ID

        mongodb_id TEXT DEFAULT NULL,                                -- MongoDB 的文档 ID 用于云同步
        last_sync_to_cloud_mongodb_time DATETIME DEFAULT NULL,       -- 最后同步到云端 MongoDB 的时间

        title TEXT,                                                  -- 笔记标题
        content TEXT,                                                -- 笔记内容
        
        mood_name TEXT DEFAULT 'happy',                              -- 心情名称 happy、sad 等
        mood_score INTEGER DEFAULT 10,                               -- 心情评分 1 - 10

        mood_app_in_img_name TEXT DEFAULT 'mood1',                   -- 应用内置心情图片名称
        mood_user_create_url TEXT DEFAULT NULL,                      -- 用户自定义心情图片URL

        yyyymm INTEGER DEFAULT 199911,                               -- 年月格式 202412 表示2024年12月
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,               -- 创建时间
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP                -- 更新时间
      );
    `);
  };

  // 如果字体还没有加载完成，显示加载界面
  if (!fontsLoaded) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Loading Fonts...</Text>
      </View>
    );
  }


  return (
    <SQLiteProvider databaseName="mooood_v2.db" onInit={createDbIfNeeded} >
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="index" />
      </Stack>
      <Toast />
    </SQLiteProvider>
  );
}










// import { Stack } from "expo-router";
// import { Platform } from "react-native";

// export default function RootLayout() {
//   return (
//     <Stack
//       screenOptions={{
//         headerShadowVisible: false, // iOS
//         headerStyle: {
//           backgroundColor: "#FFF9E0", // 设置 header 背景色
//         },
//         headerTintColor: "#000", // 设置返回箭头和文字颜色
//         contentStyle: {
//           backgroundColor: "#fff", // 设置页面内容区域背景色
//         },
//       }}
//     >
//       <Stack.Screen name="index" />
//       <Stack.Screen name="details" />
//     </Stack>
//   );
// }




// import { Stack } from 'expo-router';

// export default function RootLayout() {
//   return (
//     <Stack
//       // screenOptions={{
//       //   headerStyle: {
//       //     backgroundColor: '#f4511e',
//       //   },
//       //   headerTintColor: '#fff',
//       //   headerTitleStyle: {
//       //     fontWeight: 'bold',
//       //   },
//       // }}
//       >
//       <Stack.Screen name="index" />
//       <Stack.Screen name="details" />
//     </Stack>
//   );
// }
