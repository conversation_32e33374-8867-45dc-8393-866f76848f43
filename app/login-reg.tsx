import React, { useState, useEffect } from 'react';

import {
    View,
    Text,
    StyleSheet,
    Image,
    TouchableOpacity,
    ImageBackground,
    Animated,
    StatusBar,
    Platform,
    TextInput
} from 'react-native';

import { router } from 'expo-router';

import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons';

import Toast from 'react-native-toast-message';


export default function LoginRegScreen() {
    const insets = useSafeAreaInsets(); // 获取安全区域的边距

    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');

    const handleTopLeftBack = () => {
        router.back();
    };

    // 处理注册
    const handleLogin = async () => {
        console.log('用户名:', username);
        console.log('密码:', password);


        try {
            const response = await fetch('https://hongweizhu.com:3000/x_mooood_user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    do_sth: 'zhuce',
                    name: username,
                    password: password,
                }),
            });

            const data = await response.json();

            if (response.ok) {
                console.log('注册返回数据', data);

                if (data?.code === 1000) {
                    console.log('注册成功！！！')

                    Toast.show({
                        type: 'success',
                        text1: '注册成功',
                    });
                } else if (data?.code === 4001) {
                    console.log('用户名 不能为空')
                    Toast.show({
                        type: 'error',
                        text1: data?.msg,
                    });
                } else if (data?.code === 4002) {
                    console.log('密码 不能为空')
                    Toast.show({
                        type: 'error',
                        text1: data?.msg,
                    });
                } else if (data?.code === 4003) {
                    console.log('用户名已存在')
                    Toast.show({
                        type: 'error',
                        text1: data?.msg,
                    });
                } else if (data?.code === 4004) {
                    console.log('注册失败')

                    Toast.show({
                        type: 'error',
                        text1: data?.msg,
                    });
                } else {
                    console.log('注册失败:', data);
                }

            } else {
                console.log('注册失败了了了', data);
            }
        } catch (error) {
            console.error('网络错误:', error);
        }
    };

    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}> 注册 </Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000" // 设置为透明
                            strokeWidth={1.2}
                        />
                    </View>

                    <View style={styles.content}>
                        <View style={styles.loginForm}>
                            {/* <Text style={styles.welcomeText}>欢迎回来</Text> */}

                            <View style={styles.inputContainer}>
                                <TextInput
                                    style={styles.input}
                                    placeholder="用户名"
                                    // placeholderTextColor="#95a5a6"
                                    value={username}
                                    onChangeText={setUsername}
                                    autoCapitalize="none"
                                />
                            </View>

                            <View style={styles.inputContainer}>
                                <TextInput
                                    style={styles.input}
                                    placeholder="密码"
                                    // placeholderTextColor="#95a5a6"
                                    value={password}
                                    onChangeText={setPassword}
                                    secureTextEntry
                                    autoCapitalize="none"
                                />
                            </View>

                            <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
                                <Text style={styles.loginButtonText}> 注册 </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </ImageBackground>
        </>

    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,

        width: '100%',
        height: '100%',
    },

    all: {
        flex: 1,

        // justifyContent: 'center',
        // alignItems: 'center',
        // backgroundColor: '#f40',
    },

    head_nav: {
        height: 50,
        width: '100%',

        // paddingHorizontal: 5,

        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',

        // backgroundColor: '#f40',
    },
    head_nav_title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },


    content: {
        flex: 1,
        padding: 16,
        // backgroundColor: 'white',

    },

    loginForm: {
        alignItems: 'center',
    },

    welcomeText: {
        fontSize: 32,
        fontWeight: '700',
        textAlign: 'center',
        marginBottom: 50,
        color: '#2c3e50',
        letterSpacing: 1,
    },

    inputContainer: {
        width: '100%',
        marginBottom: 24,
    },

    input: {
        height: 56,
        borderWidth: 0,
        borderRadius: 16,
        paddingHorizontal: 20,
        fontSize: 16,
        backgroundColor: 'rgba(255, 255, 255, 0.95)',

        color: '#2c3e50',
    },

    loginButton: {
        width: '100%',
        height: 56,
        borderRadius: 16,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 32,

        backgroundColor: 'black',
    },

    loginButtonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: '600',
        letterSpacing: 1,
    },
});
